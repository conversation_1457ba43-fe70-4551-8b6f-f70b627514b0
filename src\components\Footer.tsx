"use client"

import { FaFacebook, FaGithub, FaInstagram, FaLinkedin, FaWhatsapp } from "react-icons/fa"
import { HiArrowUp } from "react-icons/hi2"

const Footer = () => {
  const socialLinks = [
    {
      icon: FaFacebook,
      href: "https://facebook.com",
      label: "Facebook"
    },
    {
      icon: FaGithub,
      href: "https://github.com",
      label: "GitHub"
    },
    {
      icon: FaInstagram,
      href: "https://instagram.com",
      label: "Instagram"
    },
    {
      icon: FaLinkedin,
      href: "https://linkedin.com",
      label: "LinkedIn"
    },
    {
      icon: FaWhatsapp,
      href: "https://whatsapp.com",
      label: "WhatsApp"
    }
  ]

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  return (
    <footer className="relative text-white py-12"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-10 left-10 w-48 h-48 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-10 right-10 w-64 h-64 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 md:px-8">
        {/* Main Footer Content */}
        <div className="text-center space-y-8">
          {/* Find Me On Section */}
          <div className="space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              FIND ME ON
            </h2>
            <p className="text-gray-300 text-lg">
              Feel free to connect with me
            </p>
          </div>

          {/* Social Icons */}
          <div className="flex justify-center items-center gap-6">
            {socialLinks.map((social, index) => {
              const IconComponent = social.icon
              return (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                  className="group"
                >
                  <div className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center transition-all duration-300 hover:scale-110 hover:border-[rgb(233,100,55)] hover:bg-[rgb(233,100,55)]">
                    <IconComponent className="w-5 h-5 text-white transition-colors duration-300" />
                  </div>
                </a>
              )
            })}
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-12 pt-8 border-t border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-300 text-sm text-center md:text-left">
              Copyright © 2024. All rights reserved. Designed & developed by Moaz Developer.
            </p>
            
            {/* Back to Top Button */}
            <button
              onClick={scrollToTop}
              className="group flex items-center gap-2 px-4 py-2 bg-transparent border-2 border-white text-white rounded-full hover:bg-[rgb(233,100,55)] hover:border-[rgb(233,100,55)] transition-all duration-300"
              aria-label="Back to top"
            >
              <HiArrowUp className="w-4 h-4 transition-transform duration-300 group-hover:translate-y-[-2px]" />
              <span className="text-sm font-medium">Back to Top</span>
            </button>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
