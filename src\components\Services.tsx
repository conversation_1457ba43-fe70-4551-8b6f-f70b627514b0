"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ExternalLink,
  Code,
  Globe,
  Smartphone,
  Database,
  GraduationCap,
  Palette,
  Zap,
  Eye,
  Calendar,
  CheckCircle,
  ArrowRight,
} from "lucide-react"
import { useState } from "react"

const services = [
  {
    id: 1,
    title: "Frontend Development",
    description:
      "Build modern, responsive web applications using React.js, Next.js, and TypeScript. Create pixel-perfect interfaces with smooth animations and optimal performance.",
    icon: Code,
    image: "/photo-1551288049-bebda4e38f71.jpeg",
    link: "#contact",
    tools: ["React.js", "Next.js", "TypeScript", "Tailwind CSS"],
    category: "Development",
    pricing: "Starting at $500",
    duration: "2-4 weeks",
    color: "from-blue-500 to-purple-600",
    features: ["Responsive Design", "Modern UI/UX", "Performance Optimization", "Cross-Browser Support"],
  },
  {
    id: 2,
    title: "Full Stack Development",
    description:
      "Complete web application development from frontend to backend using MERN stack. Database design, API integration, and deployment solutions.",
    icon: Database,
    image: "/photo-1467232004584-a241de8bcf5d.jpeg",
    link: "#contact",
    tools: ["MERN Stack", "Node.js", "MongoDB", "Express.js"],
    category: "Development",
    pricing: "Starting at $1000",
    duration: "4-8 weeks",
    color: "from-green-500 to-teal-600",
    features: ["Database Design", "API Development", "Authentication", "Deployment"],
  },
  {
    id: 3,
    title: "UI/UX Design",
    description:
      "Create intuitive and visually appealing user interfaces with focus on user experience. Wireframing, prototyping, and design system development.",
    icon: Palette,
    image: "/photo-1554224155-6726b3ff858f.jpeg",
    link: "#contact",
    tools: ["Figma", "Adobe XD", "Sketch", "Prototyping"],
    category: "Design",
    pricing: "Starting at $300",
    duration: "1-3 weeks",
    color: "from-purple-500 to-pink-600",
    features: ["Wireframing", "Prototyping", "Design Systems", "User Research"],
  },
  {
    id: 4,
    title: "Web Development Training",
    description:
      "Comprehensive web development courses and mentoring. MERN stack curriculum, hands-on projects, and personalized guidance for aspiring developers.",
    icon: GraduationCap,
    image: "/photo-1555066931-4365d14bab8c.jpeg",
    link: "#contact",
    tools: ["MERN Stack", "React.js", "Next.js", "Teaching"],
    category: "Education",
    pricing: "Starting at $200",
    duration: "4-12 weeks",
    color: "from-cyan-500 to-blue-600",
    features: ["Curriculum Development", "Hands-on Projects", "Mentoring", "Career Guidance"],
  },
  {
    id: 5,
    title: "Website Optimization",
    description:
      "Improve website performance, SEO, and user experience. Code optimization, loading speed enhancement, and mobile responsiveness improvements.",
    icon: Zap,
    image: "/photo-1460925895917-afdab827c52f.jpeg",
    link: "#contact",
    tools: ["Performance Tools", "SEO", "Analytics", "Testing"],
    category: "Optimization",
    pricing: "Starting at $400",
    duration: "1-2 weeks",
    color: "from-orange-500 to-red-600",
    features: ["Performance Audit", "SEO Optimization", "Speed Enhancement", "Mobile Optimization"],
  },
  {
    id: 6,
    title: "Consultation & Support",
    description:
      "Technical consultation for web development projects. Code review, architecture planning, technology selection, and ongoing support services.",
    icon: Eye,
    image: "/photo-1611224923853-80b023f02d71.jpeg",
    link: "#contact",
    tools: ["Code Review", "Architecture", "Planning", "Support"],
    category: "Consultation",
    pricing: "Starting at $100/hour",
    duration: "Flexible",
    color: "from-pink-500 to-rose-600",
    features: ["Code Review", "Architecture Planning", "Tech Consultation", "Ongoing Support"],
  },
]

const Services = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  return (
    <div className="min-h-screen text-white overflow-hidden"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 w-64 h-64 rounded-full blur-3xl animate-pulse delay-500"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      {/* Header */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h1 className="text-4xl md:text-6xl py-8 font-bold" style={{ color: "rgb(233, 100, 55)" }}>
              Services
            </h1>
            <div
              className="hidden md:block w-20 h-1 rounded-full"
              style={{
                background: `linear-gradient(to right, rgb(233, 100, 55), transparent)`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {services.map((service, index) => {
            const IconComponent = service.icon
            const isHovered = hoveredCard === service.id

            return (
              <div
                key={service.id}
                className="group"
                onMouseEnter={() => setHoveredCard(service.id)}
                onMouseLeave={() => setHoveredCard(null)}
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <Card
                  className={`
                    relative h-[600px] transition-all duration-500 cursor-pointer overflow-hidden
                    backdrop-blur-sm border-2
                    ${isHovered ? "scale-105" : ""}
                  `}
                  style={{
                    backgroundColor: "rgba(16, 19, 20, 0.8)",
                    borderColor: isHovered ? "rgb(233, 100, 55)" : "rgba(233, 100, 55, 0.3)",
                    boxShadow: isHovered
                      ? "0 0 30px rgba(233, 100, 55, 0.3), inset 0 0 30px rgba(233, 100, 55, 0.1)"
                      : "0 0 20px rgba(0, 0, 0, 0.3)",
                  }}
                >
                  {/* Service Icon */}
                  <div className="relative h-48 overflow-hidden">
                    <div
                      className="absolute inset-0 flex items-center justify-center"
                      style={{
                        background: `linear-gradient(135deg, rgba(233, 100, 55, 0.1), rgba(233, 100, 55, 0.05))`,
                      }}
                    >
                      <IconComponent 
                        className="w-20 h-20 transition-all duration-500 group-hover:scale-110" 
                        style={{ color: "rgb(233, 100, 55)" }} 
                      />
                    </div>
                  </div>

                  <CardContent className="px-6 pt-4 pb-4 flex-1 flex flex-col">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <Badge
                          variant="outline"
                          className="text-xs"
                          style={{
                            borderColor: "rgb(233, 100, 55)",
                            color: "rgb(233, 100, 55)",
                            backgroundColor: "rgba(233, 100, 55, 0.1)",
                          }}
                        >
                          {service.category}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-400">
                        <Calendar className="w-3 h-3" />
                        {service.duration}
                      </div>
                    </div>

                    {/* Title and Description */}
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-[rgb(233,100,55)] transition-colors">
                        {service.title}
                      </h3>
                      <p className="text-gray-400 text-sm leading-relaxed">
                        {service.description}
                      </p>
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                      <div className="text-lg font-bold" style={{ color: "rgb(233, 100, 55)" }}>
                        {service.pricing}
                      </div>
                    </div>

                    {/* Tools */}
                    <div className="mb-4">
                      <h4 className="text-white font-semibold mb-2 text-sm">Technologies:</h4>
                      <div className="flex flex-wrap gap-1">
                        {service.tools.map((tool, toolIndex) => (
                          <span
                            key={toolIndex}
                            className="px-2 py-1 text-xs rounded-full"
                            style={{
                              backgroundColor: "rgba(233, 100, 55, 0.1)",
                              color: "rgb(233, 100, 55)",
                              border: "1px solid rgba(233, 100, 55, 0.3)",
                            }}
                          >
                            {tool}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Features */}
                    <div className="mb-6 flex-1">
                      <h4 className="text-white font-semibold mb-2 text-sm">What's Included:</h4>
                      <ul className="space-y-1">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-gray-300 text-xs">
                            <CheckCircle className="w-3 h-3 mr-2 text-[rgb(233,100,55)] flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Action Button */}
                    <div className="mt-auto">
                      <Button
                        className="w-full group/btn"
                        style={{
                          backgroundColor: "rgb(233, 100, 55)",
                          borderColor: "rgb(233, 100, 55)",
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = "transparent"
                          e.currentTarget.style.color = "rgb(233, 100, 55)"
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = "rgb(233, 100, 55)"
                          e.currentTarget.style.color = "white"
                        }}
                      >
                        <a
                          href={service.link}
                          className="flex items-center justify-center gap-2 w-full"
                        >
                          Get Started
                          <ArrowRight className="w-4 h-4 transition-transform group-hover/btn:translate-x-1" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default Services
