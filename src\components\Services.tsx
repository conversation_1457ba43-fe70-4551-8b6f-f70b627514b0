"use client"

import {
  Code,
  Smartphone,
  Database,
  Server,
} from "lucide-react"

const services = [
  {
    id: 1,
    title: "Web Development",
    description:
      "Professional web development using HTML, CSS, JavaScript, and modern frameworks to build responsive and user-friendly websites.",
    icon: Code,
  },
  {
    id: 2,
    title: "Mobile App Development",
    description:
      "Native and hybrid mobile application development for Android platforms, delivering smooth and efficient user experiences.",
    icon: Smartphone,
  },
  {
    id: 3,
    title: "Database Management",
    description:
      "Experienced in database design, implementation, and management with MongoDB and Firebase for efficient data handling.",
    icon: Database,
  },
  {
    id: 4,
    title: "Backend Development",
    description:
      "Developing scalable backend solutions using Node.js and Express to ensure robust server-side functionality.",
    icon: Server,
  },
]

const Services = () => {
  return (
    <div className="min-h-screen text-white overflow-hidden"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 w-64 h-64 rounded-full blur-3xl animate-pulse delay-500"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      {/* Header */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h1 className="text-4xl md:text-6xl py-8 font-bold" style={{ color: "rgb(233, 100, 55)" }}>
              Services
            </h1>
            <div
              className="hidden md:block w-20 h-1 rounded-full"
              style={{
                background: `linear-gradient(to right, rgb(233, 100, 55), transparent)`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
          {services.map((service, index) => {
            const IconComponent = service.icon

            return (
              <div
                key={service.id}
                className="group"
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <div
                  className="relative h-[300px] transition-all duration-500 cursor-pointer overflow-hidden backdrop-blur-sm border-2 rounded-2xl p-6 flex flex-col items-center text-center"
                  style={{
                    backgroundColor: "rgba(16, 19, 20, 0.8)",
                    borderColor: "rgb(233, 100, 55)",
                    boxShadow: "0 0 20px rgba(233, 100, 55, 0.3)",
                  }}
                >
                  {/* Service Icon */}
                  <div className="mb-6">
                    <IconComponent
                      className="w-12 h-12"
                      style={{ color: "rgb(233, 100, 55)" }}
                    />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-white mb-4">
                    {service.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 text-sm leading-relaxed flex-1">
                    {service.description}
                  </p>
                </div>
              </div>
            )
          })}
        </div>

        {/* Pagination Dots */}
        <div className="flex justify-center mt-8 gap-2">
          <div className="w-3 h-3 rounded-full bg-white"></div>
          <div className="w-3 h-3 rounded-full bg-gray-600"></div>
          <div className="w-3 h-3 rounded-full bg-gray-600"></div>
        </div>
      </div>
    </div>
  )
}

export default Services
