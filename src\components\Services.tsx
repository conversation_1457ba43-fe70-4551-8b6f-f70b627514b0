"use client"

import {
  Code,
  Database,
  Globe,
  Palette,
  Zap,
  Shield,
  RefreshCw,
  Layers,
  FileText,
  Search,
} from "lucide-react"

const services = [
  {
    id: 1,
    title: "Responsive Website Development",
    description:
      "I build mobile-friendly, cross-browser compatible, and fully responsive websites using Next.js, Tailwind CSS, and React.js.",
    icon: Code,
  },
  {
    id: 2,
    title: "Next.js Static & Dynamic Web Applications",
    description:
      "I develop static and dynamic web apps using Next.js with file-based routing, API routes, SSR, SSG, and ISR.",
    icon: Globe,
  },
  {
    id: 3,
    title: "UI/UX Design to Code Conversion",
    description:
      "I convert Figma/Adobe XD/UI design mockups into fully functional pixel-perfect websites with clean code and modern design practices.",
    icon: Palette,
  },
  {
    id: 4,
    title: "Performance Optimization",
    description:
      "I optimize websites using Next.js Image Optimization, lazy loading, code splitting, and Turbopack/Webpack for faster load times.",
    icon: Zap,
  },
  {
    id: 5,
    title: "Authentication & Protected Routes",
    description:
      "I implement JWT/NextAuth.js-based login systems, manage protected pages, and handle secure client-server communication.",
    icon: Shield,
  },
  {
    id: 6,
    title: "API Integration & Data Fetching",
    description:
      "I connect frontend apps with RESTful APIs, GraphQL, or Backend as a Service using getServerSideProps, getStaticProps, Axios, fetch, React Query.",
    icon: RefreshCw,
  },
  {
    id: 7,
    title: "Reusable Component Development",
    description:
      "I develop modular, reusable components using ShadCN UI, Tailwind, and Headless UI, speeding up project development.",
    icon: Layers,
  },
  {
    id: 8,
    title: "Landing Page & Portfolio Creation",
    description:
      "I design and develop high-converting landing pages, portfolios, and company profiles with SEO and fast performance.",
    icon: FileText,
  },
  {
    id: 9,
    title: "Third-party Integrations",
    description:
      "Integrate external libraries and services like Stripe/Razorpay for payments, Google Maps, EmailJS/SMTP for contact forms, CMS integration.",
    icon: Database,
  },
  {
    id: 10,
    title: "SEO Optimization & Meta Tags Setup",
    description:
      "I implement SEO best practices including meta tags, Open Graph, robots.txt, sitemap.xml, and structured data for better search visibility.",
    icon: Search,
  },
]

const Services = () => {
  return (
    <div className="min-h-screen text-white overflow-hidden"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 w-64 h-64 rounded-full blur-3xl animate-pulse delay-500"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      {/* Header */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h1 className="text-4xl md:text-6xl py-8 font-bold" style={{ color: "rgb(233, 100, 55)" }}>
              Services
            </h1>
            <div
              className="hidden md:block w-20 h-1 rounded-full"
              style={{
                background: `linear-gradient(to right, rgb(233, 100, 55), transparent)`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 max-w-7xl mx-auto">
          {services.map((service, index) => {
            const IconComponent = service.icon

            return (
              <div
                key={service.id}
                className="group"
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <div
                  className="relative h-[300px] transition-all duration-500 cursor-pointer overflow-hidden backdrop-blur-sm border-2 rounded-2xl p-6 flex flex-col items-center text-center"
                  style={{
                    backgroundColor: "rgba(16, 19, 20, 0.8)",
                    borderColor: "rgb(233, 100, 55)",
                    boxShadow: "0 0 20px rgba(233, 100, 55, 0.3)",
                  }}
                >
                  {/* Service Icon */}
                  <div className="mb-6">
                    <IconComponent
                      className="w-12 h-12"
                      style={{ color: "rgb(233, 100, 55)" }}
                    />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-white mb-4">
                    {service.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 text-sm leading-relaxed flex-1">
                    {service.description}
                  </p>
                </div>
              </div>
            )
          })}
        </div>

        {/* Pagination Dots */}
        <div className="flex justify-center mt-8 gap-2">
          <div className="w-3 h-3 rounded-full bg-white"></div>
          <div className="w-3 h-3 rounded-full bg-gray-600"></div>
          <div className="w-3 h-3 rounded-full bg-gray-600"></div>
        </div>
      </div>
    </div>
  )
}

export default Services
