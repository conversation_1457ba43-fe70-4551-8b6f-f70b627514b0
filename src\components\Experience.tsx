"use client"
import { useEffect, useRef, useState } from "react"
import { Calendar, MapPin, Briefcase, ExternalLink, Building } from "lucide-react"

const ProfessionalExperience = () => {
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLDivElement>(null)

  const experiences = [
    {
      title: "Instructor",
      company: "Innvoytech",
      period: "09/2024 – 11/2025",
      location: "Remote",
      type: "Full-time",
      description:
        "Leading comprehensive web development courses and mentoring students in modern JavaScript frameworks. Developing curriculum for MERN Stack development and providing hands-on guidance to aspiring developers.",
      keySkills: ["MERN Stack", "React.js", "Next.js", "Teaching", "Curriculum Development", "Student Mentoring"],
      achievements: [
        "Mentored 50+ students in web development",
        "Developed comprehensive MERN Stack curriculum",
        "Achieved 95% student satisfaction rate",
      ],
    },
    {
      title: "UI Developer",
      company: "Crective",
      period: "02/2025 – 03/2025",
      location: "Remote",
      type: "Contract",
      description:
        "Developed responsive user interfaces and integrated RESTful APIs for enhanced user experiences. Collaborated with design teams to implement pixel-perfect interfaces and optimize application performance.",
      keySkills: [
        "React.js",
        "Next.js",
        "UI/UX Design",
        "API Integration",
        "Responsive Design",
        "Performance Optimization",
      ],
      achievements: [
        "Improved application performance by 40%",
        "Successfully integrated 15+ API endpoints",
        "Delivered projects ahead of schedule",
      ],
    },
    {
      title: "UI Designer/Frontend React JS Developer",
      company: "Genius Mind Zone",
      period: "12/2024 – 01/2025",
      location: "Remote",
      type: "Freelance",
      description:
        "Created intuitive user interfaces with focus on responsive design and user experience optimization. Worked closely with clients to translate business requirements into functional web applications.",
      keySkills: ["React.js", "UI Design", "Responsive Design", "User Experience", "Client Communication", "Figma"],
      achievements: [
        "Designed and developed 5+ client projects",
        "Achieved 100% client satisfaction",
        "Reduced development time by 30% through reusable components",
      ],
    },
    {
      title: "Frontend Developer",
      company: "Pak Freelancer Software House",
      period: "07/2023 – 01/2024",
      location: "On-site",
      type: "Full-time",
      description:
        "Built responsive web applications and collaborated with design teams to implement pixel-perfect interfaces. Focused on creating maintainable code and following best practices in frontend development.",
      keySkills: ["HTML5", "CSS3", "JavaScript", "Responsive Design", "Team Collaboration", "Version Control"],
      achievements: [
        "Developed 10+ responsive web applications",
        "Collaborated with cross-functional teams",
        "Maintained 99% code quality standards",
      ],
    },
  ]

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 },
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section
      ref={sectionRef}
      className="min-h-screen py-20 relative overflow-hidden"
      style={{ backgroundColor: "rgb(16, 19, 20)" }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 20% 80%, rgb(239, 68, 68) 0%, transparent 50%),
                           radial-gradient(circle at 80% 20%, rgb(239, 68, 68) 0%, transparent 50%),
                           radial-gradient(circle at 40% 40%, rgb(239, 68, 68) 0%, transparent 50%)`,
          }}
        />
      </div>

      <div className="container mx-auto px-4 md:px-8 lg:px-16 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center p-3 rounded-full bg-[rgb(233,100,55)]/10 border border-red-500/20 mb-6">
            <Briefcase className="text-[rgb(233,100,55)] text-2xl" />
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
            Professional
            <span className="block text-[rgb(233,100,55)]">Experience</span>
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            A journey through innovative projects and meaningful contributions in web development
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-red-600 mx-auto rounded-full mt-6" />
        </div>

        {/* Experience Timeline */}
        <div className="max-w-5xl mx-auto">
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 md:left-1/2 md:-translate-x-0.5 w-0.5 h-full bg-gradient-to-b from-[rgb(233,100,55)] via-[rgb(233,100,55)] to-transparent"></div>

            {experiences.map((exp, index) => (
              <div
                key={index}
                className={`relative mb-12 md:mb-16 group ${
                  index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
                } md:flex md:items-start`}
              >
                {/* Timeline Dot */}
                <div className="absolute left-6 md:left-1/2 md:-translate-x-1/2 w-4 h-4 bg-[rgb(233,100,55)] rounded-full border-4 border-gray-900 z-20 group-hover:scale-125 transition-transform duration-300 shadow-lg shadow-red-500/50">
                  <div className="absolute inset-0 bg-[rgb(233,100,55)] rounded-full animate-ping opacity-20"></div>
                </div>

                {/* Content Card */}
                <div className={`ml-16 md:ml-0 md:w-5/12 ${index % 2 === 0 ? "md:pr-8" : "md:pl-8"}`}>
                  <div
                    className={`bg-[rgb(233,100,55)] backdrop-blur-sm border border-[rgb(233,100,55)] rounded-2xl p-6 md:p-8 hover:bg-gray-900/70 hover:border-red-500/30 transition-all duration-300 group-hover:transform group-hover:scale-105 shadow-xl ${
                      isVisible ? "animate-fade-in-up" : "opacity-0"
                    }`}
                    style={{ animationDelay: `${index * 200}ms` }}
                  >
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <Building className="w-4 h-4 text-[rgb(233,100,55)] mr-2" />
                          <span className="text-[rgb(233,100,55)] text-sm font-medium uppercase tracking-wide">{exp.type}</span>
                        </div>
                        <h3 className="text-xl md:text-2xl font-bold text-white mb-2 group-hover:text-[rgb(233,100,55)] transition-colors">
                          {exp.title}
                        </h3>
                        <div className="flex items-center text-[rgb(233,100,55)] font-semibold mb-3">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          {exp.company}
                        </div>
                      </div>
                    </div>

                    {/* Meta Information */}
                    <div className="flex flex-wrap gap-4 mb-4 text-sm">
                      <div className="flex items-center text-gray-300">
                        <Calendar className="w-4 h-4 mr-2 text-[rgb(233,100,55)]" />
                        {exp.period}
                      </div>
                      <div className="flex items-center text-gray-300">
                        <MapPin className="w-4 h-4 mr-2 text-[rgb(233,100,55)]" />
                        {exp.location}
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-gray-400 mb-6 leading-relaxed">{exp.description}</p>

                    {/* Key Achievements */}
                    <div className="mb-6">
                      <h4 className="text-white font-semibold mb-3">Key Achievements:</h4>
                      <ul className="space-y-2">
                        {exp.achievements.map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start text-gray-300 text-sm">
                            <div className="w-1.5 h-1.5 bg-[rgb(233,100,55)] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Skills */}
                    <div>
                      <h4 className="text-white font-semibold mb-3">Technologies & Skills:</h4>
                      <div className="flex flex-wrap gap-2">
                        {exp.keySkills.map((skill, skillIndex) => (
                          <span
                            key={skillIndex}
                            className="px-3 py-1.5 bg-[rgb(233,100,55)]/10 border border-red-500/20 text-[rgb(233,100,55)] text-xs font-medium rounded-full hover:bg-[rgb(233,100,55)]/20 hover:border-red-500/40 transition-all duration-200"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Spacer for desktop layout */}
                <div className="hidden md:block md:w-2/12"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="text-center mt-16">
          <div className="bg-[rgb(233,100,55)] backdrop-blur-sm border border-[rgb(233,100,55)] rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-8">Career Highlights</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-[rgb(233,100,55)] mb-2">7-8 months</div>
                <div className="text-gray-400 text-sm uppercase tracking-wide">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[rgb(233,100,55)] mb-2">4</div>
                <div className="text-gray-400 text-sm uppercase tracking-wide">Companies</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[rgb(233,100,55)] mb-2">20+</div>
                <div className="text-gray-400 text-sm uppercase tracking-wide">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[rgb(233,100,55)] mb-2">50+</div>
                <div className="text-gray-400 text-sm uppercase tracking-wide">Students Mentored</div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        
      </div>

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out forwards;
        }
      `}</style>
    </section>
  )
}

export default ProfessionalExperience
