"use client"

import { useState } from "react"
import { Send } from "lucide-react"

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: ""
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Form submitted:", formData)
  }

  return (
    <div className="min-h-screen text-white overflow-hidden"
    style={{backgroundColor: 'rgb(16, 19, 20)'}}>
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl animate-pulse delay-1000"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 w-64 h-64 rounded-full blur-3xl animate-pulse delay-500"
          style={{ backgroundColor: "rgb(233, 100, 55)" }}
        ></div>
      </div>

      {/* Header */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h1 className="text-4xl md:text-6xl py-8 font-bold" style={{ color: "rgb(233, 100, 55)" }}>
              Contact Me
            </h1>
            <div
              className="hidden md:block w-20 h-1 rounded-full"
              style={{
                background: `linear-gradient(to right, rgb(233, 100, 55), transparent)`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Contact Content */}
      <div className="relative z-10 px-6 md:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16">
            {/* Contact Form */}
            <div className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Input */}
                <div>
                  <input
                    type="text"
                    name="name"
                    placeholder="Enter Your Name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full bg-transparent border-b-2 border-white text-white placeholder-white py-3 px-0 focus:outline-none focus:border-[rgb(233,100,55)] transition-colors duration-300"
                    required
                  />
                </div>

                {/* Email Input */}
                <div>
                  <input
                    type="email"
                    name="email"
                    placeholder="Enter Your Email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full bg-transparent border-b-2 border-white text-white placeholder-white py-3 px-0 focus:outline-none focus:border-[rgb(233,100,55)] transition-colors duration-300"
                    required
                  />
                </div>

                {/* Message Input */}
                <div>
                  <textarea
                    name="message"
                    placeholder="Enter Your Message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={6}
                    className="w-full bg-transparent border-b-2 border-white text-white placeholder-white py-3 px-0 focus:outline-none focus:border-[rgb(233,100,55)] transition-colors duration-300 resize-none"
                    required
                  />
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <button
                    type="submit"
                    className="flex items-center gap-2 px-8 py-3 bg-transparent border-2 border-[rgb(233,100,55)] text-[rgb(233,100,55)] rounded-full hover:bg-[rgb(233,100,55)] hover:text-white transition-all duration-300 font-medium"
                  >
                    <Send className="w-4 h-4" />
                    Send
                  </button>
                </div>
              </form>
            </div>

            {/* Map Section */}
            <div className="h-[500px] rounded-lg overflow-hidden">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3619.4!2d67.0361!3d24.9056!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3eb33f90daa94c2b%3A0x2b2b2b2b2b2b2b2b!2sAmin%20Town%2C%20Karachi%2C%20Pakistan!5e0!3m2!1sen!2s!4v1699999999999!5m2!1sen!2s"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Amin Town Location Map"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Contact
