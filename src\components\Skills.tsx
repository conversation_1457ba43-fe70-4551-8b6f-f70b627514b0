"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { Code2, Braces, Globe, FileCode, Server, Zap, Database, PiIcon as Python } from "lucide-react"

interface Skill {
  name: string
  icon: React.ComponentType<{ className?: string }>
  percentage: number
  color: string
  category: string
}

const Skills = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [animatedPercentages, setAnimatedPercentages] = useState<number[]>([])
  const sectionRef = useRef<HTMLElement>(null)

  const skillsData: Skill[] = [
    { name: "React", icon: Code2, percentage: 90, color: "#61DAFB", category: "Frontend" },
    { name: "JavaScript", icon: Braces, percentage: 85, color: "#F7DF1E", category: "Language" },
    { name: "Next.js", icon: Globe, percentage: 88, color: "#000000", category: "Framework" },
    { name: "TypeScript", icon: FileCode, percentage: 80, color: "#3178C6", category: "Language" },
    { name: "Node.js", icon: Server, percentage: 75, color: "#339933", category: "Backend" },
    { name: "Express", icon: Zap, percentage: 70, color: "#000000", category: "Framework" },
    { name: "MongoDB", icon: Database, percentage: 72, color: "#47A248", category: "Database" },
    { name: "Python", icon: Python, percentage: 65, color: "#3776AB", category: "Language" },
  ]

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
          animateSkills()
        }
      },
      { threshold: 0.2 },
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [isVisible])

  const animateSkills = () => {
    skillsData.forEach((skill, index) => {
      setTimeout(() => {
        let currentPercentage = 0
        const increment = skill.percentage / 80 // Smooth animation over 80 frames

        const animate = () => {
          currentPercentage += increment
          if (currentPercentage >= skill.percentage) {
            currentPercentage = skill.percentage
          }

          setAnimatedPercentages((prev) => {
            const newPercentages = [...prev]
            newPercentages[index] = Math.round(currentPercentage)
            return newPercentages
          })

          if (currentPercentage < skill.percentage) {
            requestAnimationFrame(animate)
          }
        }
        requestAnimationFrame(animate)
      }, index * 100) // Stagger animation
    })
  }

  // Initialize animated percentages
  useEffect(() => {
    setAnimatedPercentages(new Array(skillsData.length).fill(0))
  }, [])

  const SkillBar = ({
    skill,
    percentage,
    index,
  }: {
    skill: Skill
    percentage: number
    index: number
  }) => {
    const Icon = skill.icon

    return (
      <div
        className="group relative backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 hover:border-[rgb(233,100,55)]/30 transition-all duration-300 hover:shadow-lg hover:shadow-[rgb(233,100,55)]/10"
        style={{
          backgroundColor: "rgba(16, 19, 20, 0.8)",
          animationDelay: `${index * 100}ms`,
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div
              className="p-2 rounded-lg border transition-colors duration-300"
              style={{
                backgroundColor: "rgba(233, 100, 55, 0.1)",
                borderColor: "rgba(233, 100, 55, 0.3)",
              }}
            >
              <Icon className="w-5 h-5 transition-colors duration-300 text-[rgb(233,100,55)]" />
            </div>
            <div>
              <h3 className="font-semibold text-white text-lg">{skill.name}</h3>
              <p className="text-gray-400 text-sm">{skill.category}</p>
            </div>
          </div>
          <div className="text-right">
            <span className="font-bold text-xl text-[rgb(233,100,55)]">{percentage}%</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="relative">
          {/* Background Bar */}
          <div
            className="w-full h-3 rounded-full overflow-hidden"
            style={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
          >
            {/* Progress Fill */}
            <div
              className="h-full rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
              style={{
                width: `${percentage}%`,
                background: `linear-gradient(90deg, rgba(233, 100, 55, 0.8), rgb(233, 100, 55))`,
                boxShadow: `0 0 10px rgba(233, 100, 55, 0.4)`,
              }}
            >
              {/* Shimmer Effect */}
              <div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 animate-shimmer"
                style={{
                  animation: isVisible ? "shimmer 2s ease-in-out infinite" : "none",
                  animationDelay: `${index * 200}ms`,
                }}
              />
            </div>
          </div>

          {/* Glow Effect */}
          <div
            className="absolute inset-0 h-3 rounded-full opacity-50 blur-sm transition-opacity duration-300 group-hover:opacity-75"
            style={{
              background: `linear-gradient(90deg, transparent, rgba(233, 100, 55, 0.4), transparent)`,
              width: `${percentage}%`,
            }}
          />
        </div>

        {/* Hover Overlay */}
        <div
          className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
          style={{ background: "linear-gradient(135deg, rgba(233, 100, 55, 0.05), rgba(233, 100, 55, 0.1))" }}
        />
      </div>
    )
  }

  return (
    <section
      ref={sectionRef}
      className="min-h-screen flex flex-col justify-center py-20"
      style={{ backgroundColor: "rgb(16, 19, 20)" }}
    >
      <div className="container mx-auto px-4 md:px-8 lg:px-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full border mb-6"
            style={{ backgroundColor: "rgba(233, 100, 55, 0.1)", borderColor: "rgba(233, 100, 55, 0.2)" }}
          >
            <Code2 className="w-4 h-4 text-[rgb(233,100,55)]" />
            <span className="text-[rgb(233,100,55)] font-medium">Technical Skills</span>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent mb-4">
            Skills & Expertise
          </h2>

          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            A comprehensive overview of my technical proficiencies and development capabilities
          </p>
        </div>

        {/* Skills Grid */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {skillsData.map((skill, index) => (
              <SkillBar key={skill.name} skill={skill} percentage={animatedPercentages[index] || 0} index={index} />
            ))}
          </div>
        </div>

        {/* Stats Summary */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
          <div
            className="text-center p-4 rounded-lg border"
            style={{ backgroundColor: "rgba(16, 19, 20, 0.8)", borderColor: "rgba(233, 100, 55, 0.2)" }}
          >
            <div className="text-2xl font-bold text-[rgb(233,100,55)] mb-1">8+</div>
            <div className="text-white text-sm">Technologies</div>
          </div>
          <div
            className="text-center p-4 rounded-lg border"
            style={{ backgroundColor: "rgba(16, 19, 20, 0.8)", borderColor: "rgba(233, 100, 55, 0.2)" }}
          >
            <div className="text-2xl font-bold text-[rgb(233,100,55)] mb-1">7-8 months</div>
            <div className="text-white text-sm">Years Experience</div>
          </div>
          <div
            className="text-center p-4 rounded-lg border"
            style={{ backgroundColor: "rgba(16, 19, 20, 0.8)", borderColor: "rgba(233, 100, 55, 0.2)" }}
          >
            <div className="text-2xl font-bold text-[rgb(233,100,55)] mb-1">10+</div>
            <div className="text-white text-sm">Projects</div>
          </div>
          <div
            className="text-center p-4 rounded-lg border"
            style={{ backgroundColor: "rgba(16, 19, 20, 0.8)", borderColor: "rgba(233, 100, 55, 0.2)" }}
          >
            <div className="text-2xl font-bold text-[rgb(233,100,55)] mb-1">100%</div>
            <div className="text-white text-sm">Dedication</div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%) skewX(-12deg); }
          100% { transform: translateX(200%) skewX(-12deg); }
        }
      `}</style>
    </section>
  )
}

export default Skills
